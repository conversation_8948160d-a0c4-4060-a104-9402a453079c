#!/usr/bin/env python3
"""
Диагностический скрипт для анализа проблем парсера Uzum
"""

import time
import json
from datetime import datetime
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager
from selenium_stealth import stealth
from bs4 import BeautifulSoup
from curl_cffi import requests

def setup_driver(headless=False):
    """Настройка драйвера с детальным логированием"""
    print("🔧 Настройка Chrome драйвера...")
    
    options = webdriver.ChromeOptions()
    if headless:
        options.add_argument('--headless')
    options.add_argument('--no-sandbox')
    options.add_argument('--disable-dev-shm-usage')
    options.add_argument('--disable-blink-features=AutomationControlled')
    options.add_experimental_option("excludeSwitches", ["enable-automation"])
    options.add_experimental_option('useAutomationExtension', False)
    
    try:
        service = Service(ChromeDriverManager().install())
        driver = webdriver.Chrome(service=service, options=options)
        print("✅ ChromeDriver настроен")
    except Exception as e:
        print(f"❌ Ошибка настройки ChromeDriver: {e}")
        return None
    
    # Применяем stealth
    stealth(driver,
            languages=["ru-RU", "ru", "en-US", "en"],
            vendor="Google Inc.",
            platform="Win32",
            webgl_vendor="Intel Inc.",
            renderer="Intel Iris OpenGL Engine",
            fix_hairline=True)
    
    driver.maximize_window()
    return driver

def test_main_page_access():
    """Тестирование доступа к главной странице"""
    print("\n🏠 Тестирование главной страницы uzum.uz")
    print("-" * 50)
    
    driver = setup_driver(headless=True)
    if not driver:
        return False
    
    try:
        print("📡 Переход на главную страницу...")
        driver.get("https://uzum.uz/ru")
        time.sleep(3)
        
        print(f"📄 Заголовок страницы: {driver.title}")
        print(f"🔗 Текущий URL: {driver.current_url}")
        
        # Проверяем размер страницы
        page_source_length = len(driver.page_source)
        print(f"📏 Размер HTML: {page_source_length} символов")
        
        if page_source_length < 1000:
            print("⚠️ Страница слишком маленькая, возможно не загрузилась")
            return False
        
        # Ищем ключевые элементы
        soup = BeautifulSoup(driver.page_source, 'html.parser')
        
        # Проверяем наличие товаров
        product_selectors = [
            '[data-testid="product-card"]',
            '.product-card',
            '[class*="product"]',
            'a[href*="/product/"]',
            'a[href*="/goods/"]'
        ]
        
        found_products = 0
        for selector in product_selectors:
            elements = soup.select(selector)
            if elements:
                found_products = len(elements)
                print(f"✅ Найдено {found_products} элементов с селектором: {selector}")
                break
        
        if found_products == 0:
            print("❌ Товары не найдены ни одним селектором")
            
            # Анализируем структуру страницы
            print("\n🔍 Анализ структуры страницы:")
            
            # Ищем все ссылки
            all_links = soup.find_all('a', href=True)
            product_links = [link for link in all_links if '/product/' in link.get('href', '')]
            print(f"🔗 Всего ссылок: {len(all_links)}")
            print(f"🛍️ Ссылок на товары: {len(product_links)}")
            
            # Ищем элементы с классами, содержащими "product"
            product_classes = soup.find_all(attrs={"class": lambda x: x and any("product" in cls.lower() for cls in x)})
            print(f"📦 Элементов с классом 'product': {len(product_classes)}")
            
            # Показываем первые несколько классов
            if product_classes:
                print("🏷️ Примеры классов:")
                for i, elem in enumerate(product_classes[:5]):
                    classes = elem.get('class', [])
                    print(f"   {i+1}. {' '.join(classes)}")
        
        return found_products > 0
        
    except Exception as e:
        print(f"❌ Ошибка при тестировании главной страницы: {e}")
        return False
    finally:
        driver.quit()

def test_search_page(query="телефон"):
    """Тестирование страницы поиска"""
    print(f"\n🔍 Тестирование поиска: '{query}'")
    print("-" * 50)
    
    driver = setup_driver(headless=True)
    if not driver:
        return False
    
    try:
        search_url = f"https://uzum.uz/ru/search?q={query}"
        print(f"📡 Переход на страницу поиска: {search_url}")
        
        driver.get(search_url)
        time.sleep(3)
        
        print(f"📄 Заголовок: {driver.title}")
        print(f"🔗 URL: {driver.current_url}")
        
        # Прокручиваем страницу
        print("📜 Прокрутка страницы для загрузки товаров...")
        for i in range(10):
            driver.execute_script('window.scrollBy(0, 500)')
            time.sleep(0.2)
        
        soup = BeautifulSoup(driver.page_source, 'html.parser')
        
        # Ищем товары
        product_selectors = [
            '[data-testid="product-card"]',
            '.product-card',
            '[class*="product"]',
            'a[href*="/product/"]'
        ]
        
        found_products = 0
        working_selector = None
        
        for selector in product_selectors:
            elements = soup.select(selector)
            if elements:
                found_products = len(elements)
                working_selector = selector
                print(f"✅ Найдено {found_products} товаров с селектором: {selector}")
                break
        
        if found_products == 0:
            print("❌ Товары не найдены")
            
            # Детальный анализ
            print("\n🔍 Детальный анализ страницы поиска:")
            
            # Проверяем, есть ли сообщение "ничего не найдено"
            no_results_texts = ["ничего не найдено", "не найдено", "no results", "нет результатов"]
            page_text = soup.get_text().lower()
            
            for text in no_results_texts:
                if text in page_text:
                    print(f"⚠️ Найдено сообщение: '{text}'")
                    break
            
            # Ищем элементы поиска
            search_elements = soup.find_all(attrs={"class": lambda x: x and any("search" in cls.lower() for cls in x)})
            print(f"🔍 Элементов с классом 'search': {len(search_elements)}")
            
            # Сохраняем HTML для анализа
            with open(f'debug_search_{query}.html', 'w', encoding='utf-8') as f:
                f.write(driver.page_source)
            print(f"💾 HTML сохранен в debug_search_{query}.html")
        
        else:
            # Анализируем найденные товары
            print(f"\n📊 Анализ найденных товаров (селектор: {working_selector}):")
            
            elements = soup.select(working_selector)
            for i, element in enumerate(elements[:3]):  # Первые 3 товара
                print(f"\n🛍️ Товар {i+1}:")
                
                # Ищем название
                title_selectors = ['h3', 'h4', '[class*="title"]', '[class*="name"]']
                title = "Не найдено"
                for sel in title_selectors:
                    title_elem = element.select_one(sel)
                    if title_elem:
                        title = title_elem.get_text(strip=True)
                        break
                
                # Ищем ссылку
                link_elem = element if element.name == 'a' else element.find('a', href=True)
                link = link_elem.get('href', 'Не найдено') if link_elem else 'Не найдено'
                
                print(f"   📝 Название: {title}")
                print(f"   🔗 Ссылка: {link}")
        
        return found_products > 0
        
    except Exception as e:
        print(f"❌ Ошибка при тестировании поиска: {e}")
        return False
    finally:
        driver.quit()

def test_product_page():
    """Тестирование страницы товара"""
    print("\n🛍️ Тестирование страницы товара")
    print("-" * 50)
    
    # Сначала найдем реальную ссылку на товар
    driver = setup_driver(headless=True)
    if not driver:
        return False
    
    try:
        # Идем на главную и ищем первый товар
        print("📡 Поиск реального товара на главной странице...")
        driver.get("https://uzum.uz/ru")
        time.sleep(3)
        
        soup = BeautifulSoup(driver.page_source, 'html.parser')
        
        # Ищем первую ссылку на товар
        product_link = None
        selectors = ['a[href*="/product/"]', 'a[href*="/goods/"]']
        
        for selector in selectors:
            links = soup.select(selector)
            if links:
                product_link = links[0].get('href')
                break
        
        if not product_link:
            print("❌ Не найдена ссылка на товар")
            return False
        
        # Формируем полный URL
        if product_link.startswith('/'):
            product_url = f"https://uzum.uz{product_link}"
        else:
            product_url = product_link
        
        print(f"🔗 Найдена ссылка на товар: {product_url}")
        
        # Переходим на страницу товара
        print("📡 Переход на страницу товара...")
        driver.get(product_url)
        time.sleep(3)
        
        print(f"📄 Заголовок: {driver.title}")
        
        soup = BeautifulSoup(driver.page_source, 'html.parser')
        
        # Анализируем элементы товара
        print("\n📊 Анализ элементов товара:")
        
        # Название
        title_selectors = ['h1', '[data-testid="product-title"]', '.product-title']
        title = "Не найдено"
        for sel in title_selectors:
            elem = soup.select_one(sel)
            if elem:
                title = elem.get_text(strip=True)
                break
        print(f"📝 Название: {title}")
        
        # Цена
        price_selectors = ['[data-testid="price-current"]', '.price', '[class*="price"]']
        price = "Не найдено"
        for sel in price_selectors:
            elem = soup.select_one(sel)
            if elem:
                price = elem.get_text(strip=True)
                break
        print(f"💰 Цена: {price}")
        
        # Изображение
        img_selectors = ['[data-testid="product-image"] img', '.product-image img', 'img[alt*="товар"]']
        image = "Не найдено"
        for sel in img_selectors:
            elem = soup.select_one(sel)
            if elem:
                image = elem.get('src', elem.get('data-src', 'Атрибут src не найден'))
                break
        print(f"🖼️ Изображение: {image}")
        
        return True
        
    except Exception as e:
        print(f"❌ Ошибка при тестировании товара: {e}")
        return False
    finally:
        driver.quit()

def test_api_access():
    """Тестирование API доступа"""
    print("\n🌐 Тестирование API доступа")
    print("-" * 50)
    
    try:
        session = requests.Session()
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        }
        session.headers.update(headers)
        
        # Тестируем главную страницу
        print("📡 Тестирование доступа к главной странице...")
        response = session.get("https://uzum.uz/ru", timeout=10)
        
        print(f"📊 Статус код: {response.status_code}")
        print(f"📏 Размер ответа: {len(response.content)} байт")
        
        if response.status_code == 200:
            print("✅ API доступ работает")
            
            # Проверяем содержимое
            content = response.text.lower()
            if 'uzum' in content:
                print("✅ Контент содержит 'uzum'")
            if 'товар' in content or 'product' in content:
                print("✅ Контент содержит информацию о товарах")
                
            return True
        else:
            print(f"❌ Ошибка API: статус {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Ошибка API доступа: {e}")
        return False

def main():
    """Основная диагностическая функция"""
    print("🔍 ДИАГНОСТИКА ПАРСЕРА UZUM MARKETPLACE")
    print("=" * 60)
    print(f"⏰ Время: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    results = {}
    
    # Тест 1: API доступ
    results['api_access'] = test_api_access()
    
    # Тест 2: Главная страница
    results['main_page'] = test_main_page_access()
    
    # Тест 3: Поиск
    results['search'] = test_search_page("телефон")
    
    # Тест 4: Страница товара
    results['product_page'] = test_product_page()
    
    # Итоги
    print("\n" + "=" * 60)
    print("📋 ИТОГИ ДИАГНОСТИКИ")
    print("=" * 60)
    
    for test_name, result in results.items():
        status = "✅ ПРОЙДЕН" if result else "❌ ПРОВАЛЕН"
        print(f"{test_name.upper():20} | {status}")
    
    passed = sum(results.values())
    total = len(results)
    
    print(f"\n📊 Результат: {passed}/{total} тестов пройдено")
    
    if passed == total:
        print("🎉 Все тесты пройдены! Парсер работает корректно.")
    else:
        print("⚠️ Есть проблемы, требующие внимания.")
        
        print("\n🔧 РЕКОМЕНДАЦИИ:")
        if not results['api_access']:
            print("- Проверьте интернет-соединение")
            print("- Убедитесь, что uzum.uz доступен")
        
        if not results['main_page']:
            print("- Обновите селекторы для поиска товаров")
            print("- Проверьте изменения в структуре сайта")
        
        if not results['search']:
            print("- Проверьте URL поиска")
            print("- Обновите селекторы для страницы поиска")
        
        if not results['product_page']:
            print("- Обновите селекторы для страницы товара")
            print("- Проверьте формат URL товаров")

if __name__ == "__main__":
    main()
