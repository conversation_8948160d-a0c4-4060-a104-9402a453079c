#!/usr/bin/env python3
"""
Тестовый скрипт для демонстрации системы логирования
"""

import requests
import time
import json

def test_admin_panel_logging():
    """Тестирование логирования в админ панели"""
    base_url = "http://localhost:5000"
    
    print("🧪 Тестирование системы логирования админ панели")
    print("=" * 60)
    
    # Проверяем доступность админ панели
    try:
        response = requests.get(f"{base_url}/api/status", timeout=5)
        if response.status_code == 200:
            print("✅ Админ панель доступна")
        else:
            print("❌ Админ панель недоступна")
            return
    except Exception as e:
        print(f"❌ Ошибка подключения к админ панели: {e}")
        print("💡 Убедитесь, что админ панель запущена: python start_admin.py")
        return
    
    # Тестируем различные операции для генерации логов
    test_operations = [
        {
            "name": "Тест товара",
            "method": "POST",
            "endpoint": "/api/test-product",
            "data": {"url": "/ru/product/test-123"}
        },
        {
            "name": "Поиск товаров",
            "method": "POST", 
            "endpoint": "/api/search",
            "data": {"query": "тест", "max_pages": 1}
        },
        {
            "name": "Очистка результатов",
            "method": "POST",
            "endpoint": "/api/clear-results",
            "data": {}
        }
    ]
    
    print("\n📝 Генерация тестовых логов...")
    
    for operation in test_operations:
        try:
            print(f"  🔄 {operation['name']}...")
            
            if operation['method'] == 'POST':
                response = requests.post(
                    f"{base_url}{operation['endpoint']}", 
                    json=operation['data'],
                    timeout=10
                )
            else:
                response = requests.get(f"{base_url}{operation['endpoint']}", timeout=10)
            
            if response.status_code in [200, 201]:
                print(f"    ✅ {operation['name']} - успешно")
            else:
                print(f"    ⚠️ {operation['name']} - статус {response.status_code}")
                
        except Exception as e:
            print(f"    ❌ {operation['name']} - ошибка: {e}")
        
        time.sleep(1)  # Пауза между операциями
    
    # Получаем и отображаем логи
    print("\n📋 Получение логов...")
    
    try:
        response = requests.get(f"{base_url}/api/logs", timeout=5)
        
        if response.status_code == 200:
            logs = response.json()
            
            print(f"✅ Получено {len(logs)} записей логов")
            print("\n📄 Последние 10 записей:")
            print("-" * 60)
            
            for log in logs[:10]:
                timestamp = log.get('timestamp', 'N/A')
                level = log.get('level', 'N/A')
                message = log.get('message', 'N/A')
                
                # Форматируем время
                try:
                    from datetime import datetime
                    dt = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
                    time_str = dt.strftime('%H:%M:%S')
                except:
                    time_str = timestamp[:8] if len(timestamp) > 8 else timestamp
                
                # Цветовое кодирование уровней
                level_colors = {
                    'INFO': '🟢',
                    'ERROR': '🔴', 
                    'WARNING': '🟡',
                    'DEBUG': '🔵'
                }
                
                level_icon = level_colors.get(level, '⚪')
                
                print(f"{level_icon} [{time_str}] {level:7} | {message}")
            
            if len(logs) > 10:
                print(f"\n... и еще {len(logs) - 10} записей")
                
        else:
            print(f"❌ Ошибка получения логов: статус {response.status_code}")
            
    except Exception as e:
        print(f"❌ Ошибка получения логов: {e}")
    
    print("\n" + "=" * 60)
    print("🎯 Тестирование завершено")
    print("\n💡 Для просмотра логов в реальном времени:")
    print("   1. Откройте админ панель: http://localhost:5000")
    print("   2. Перейдите на вкладку 'Логи'")
    print("   3. Выполните любые операции в админ панели")
    print("   4. Логи будут обновляться автоматически каждые 5 секунд")

def show_log_file():
    """Показать содержимое файла логов"""
    import os
    
    log_file = "logs/uzum_parser.log"
    
    if os.path.exists(log_file):
        print(f"\n📁 Содержимое файла логов: {log_file}")
        print("-" * 60)
        
        try:
            with open(log_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
                
            # Показываем последние 20 строк
            for line in lines[-20:]:
                print(line.rstrip())
                
            if len(lines) > 20:
                print(f"\n... файл содержит {len(lines)} строк всего")
                
        except Exception as e:
            print(f"❌ Ошибка чтения файла: {e}")
    else:
        print(f"\n📁 Файл логов не найден: {log_file}")
        print("💡 Запустите админ панель для создания файла логов")

if __name__ == "__main__":
    test_admin_panel_logging()
    show_log_file()
