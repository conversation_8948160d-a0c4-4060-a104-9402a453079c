#!/usr/bin/env python3
"""
Тестирование обновленного парсера с обходом CAPTCHA
"""

import time
import json
from datetime import datetime
from uzum_parser import init_webdriver, get_searchpage_cards, check_and_handle_captcha

def test_captcha_handling():
    """Тестирование обработки CAPTCHA"""
    print("🛡️ Тестирование обработки CAPTCHA")
    print("-" * 50)
    
    driver = init_webdriver()
    
    try:
        # Тестируем на странице поиска
        test_url = "https://uzum.uz/ru/search?q=телефон"
        print(f"📡 Переход на: {test_url}")
        
        driver.get(test_url)
        time.sleep(3)
        
        # Проверяем CAPTCHA
        captcha_result = check_and_handle_captcha(driver)
        
        if captcha_result:
            print("✅ CAPTCHA обработана успешно или отсутствует")
            
            # Проверяем содержимое страницы
            page_source = driver.page_source
            print(f"📏 Размер страницы: {len(page_source)} символов")
            
            if 'uzum' in page_source.lower():
                print("✅ Страница содержит контент Uzum")
            else:
                print("⚠️ Страница может быть заблокирована")
                
        else:
            print("❌ Не удалось обработать CAPTCHA")
            
        return captcha_result
        
    finally:
        driver.quit()

def test_search_with_improvements():
    """Тестирование улучшенного поиска"""
    print("\n🔍 Тестирование улучшенного поиска")
    print("-" * 50)
    
    driver = init_webdriver()
    
    try:
        # Тестируем поиск
        search_query = "триммер"
        print(f"🔎 Поиск товаров: '{search_query}'")
        
        start_time = time.time()
        results = get_searchpage_cards(driver, search_query, max_pages=1)
        end_time = time.time()
        
        print(f"⏱️ Время выполнения: {end_time - start_time:.1f} секунд")
        print(f"📊 Найдено товаров: {len(results)}")
        
        if results:
            print(f"\n📦 Примеры найденных товаров:")
            for i, product in enumerate(results[:3], 1):
                print(f"   {i}. ID: {product.get('product_id', 'N/A')}")
                print(f"      Название: {product.get('full_name', product.get('short_name', 'N/A'))}")
                print(f"      Цена: {product.get('price', 'N/A')}")
                print(f"      URL: {product.get('url', 'N/A')}")
                print()
            
            # Сохраняем результаты
            with open('test_search_results.json', 'w', encoding='utf-8') as f:
                json.dump(results, f, ensure_ascii=False, indent=2)
            print(f"💾 Результаты сохранены в test_search_results.json")
            
            return True
        else:
            print("❌ Товары не найдены")
            return False
            
    except Exception as e:
        print(f"❌ Ошибка при тестировании поиска: {e}")
        return False
    finally:
        driver.quit()

def test_admin_panel_integration():
    """Тестирование интеграции с админ панелью"""
    print("\n🔗 Тестирование интеграции с админ панелью")
    print("-" * 50)
    
    try:
        import requests
        
        # Проверяем доступность админ панели
        response = requests.get("http://localhost:5000/api/status", timeout=5)
        
        if response.status_code == 200:
            print("✅ Админ панель доступна")
            
            # Запускаем поиск через админ панель
            search_data = {
                "query": "тест_обновленного_парсера",
                "max_pages": 1
            }
            
            print(f"📡 Запуск поиска через админ панель: {search_data}")
            search_response = requests.post(
                "http://localhost:5000/api/search",
                json=search_data,
                timeout=10
            )
            
            if search_response.status_code == 200:
                result = search_response.json()
                print(f"✅ Поиск запущен: {result}")
                
                # Ждем завершения
                print("⏳ Ожидание завершения...")
                for _ in range(30):  # Максимум 60 секунд
                    time.sleep(2)
                    status_response = requests.get("http://localhost:5000/api/status")
                    if status_response.status_code == 200:
                        status = status_response.json()
                        if not status.get('running', False):
                            print(f"✅ Поиск завершен")
                            print(f"📊 Найдено товаров: {status.get('total_products', 0)}")
                            print(f"❌ Ошибок: {status.get('errors', 0)}")
                            break
                
                return True
            else:
                print(f"❌ Ошибка запуска поиска: {search_response.status_code}")
                return False
        else:
            print(f"❌ Админ панель недоступна: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Ошибка интеграции с админ панелью: {e}")
        print("💡 Убедитесь, что админ панель запущена: python start_admin.py")
        return False

def analyze_debug_files():
    """Анализ отладочных файлов"""
    print("\n📁 Анализ отладочных файлов")
    print("-" * 50)
    
    import os
    
    debug_files = [
        'debug_search_page_1.html',
        'search_page_analysis.html',
        'product_page_analysis.html'
    ]
    
    for file_name in debug_files:
        if os.path.exists(file_name):
            file_size = os.path.getsize(file_name)
            print(f"📄 {file_name}: {file_size} байт")
            
            # Быстрый анализ содержимого
            try:
                with open(file_name, 'r', encoding='utf-8') as f:
                    content = f.read().lower()
                
                if 'captcha' in content:
                    print(f"   ⚠️ Содержит CAPTCHA")
                elif 'uzum' in content:
                    print(f"   ✅ Содержит контент Uzum")
                else:
                    print(f"   ❓ Неопределенное содержимое")
                    
            except Exception as e:
                print(f"   ❌ Ошибка чтения: {e}")
        else:
            print(f"📄 {file_name}: не найден")

def main():
    """Основная функция тестирования"""
    print("🧪 ТЕСТИРОВАНИЕ ОБНОВЛЕННОГО UZUM PARSER")
    print("=" * 60)
    print(f"⏰ Время: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    results = {}
    
    # Тест 1: Обработка CAPTCHA
    print("\n" + "="*60)
    results['captcha_handling'] = test_captcha_handling()
    
    # Тест 2: Улучшенный поиск
    print("\n" + "="*60)
    results['improved_search'] = test_search_with_improvements()
    
    # Тест 3: Интеграция с админ панелью
    print("\n" + "="*60)
    results['admin_integration'] = test_admin_panel_integration()
    
    # Анализ отладочных файлов
    print("\n" + "="*60)
    analyze_debug_files()
    
    # Итоги
    print("\n" + "=" * 60)
    print("📋 ИТОГИ ТЕСТИРОВАНИЯ")
    print("=" * 60)
    
    for test_name, result in results.items():
        status = "✅ ПРОЙДЕН" if result else "❌ ПРОВАЛЕН"
        print(f"{test_name.upper():20} | {status}")
    
    passed = sum(results.values())
    total = len(results)
    
    print(f"\n📊 Результат: {passed}/{total} тестов пройдено")
    
    if passed == total:
        print("🎉 Все тесты пройдены! Обновленный парсер работает корректно.")
    else:
        print("⚠️ Есть проблемы, требующие внимания.")
        
        print("\n🔧 РЕКОМЕНДАЦИИ:")
        if not results.get('captcha_handling'):
            print("- Улучшить обход CAPTCHA")
            print("- Добавить больше задержек")
            print("- Использовать прокси")
        
        if not results.get('improved_search'):
            print("- Проверить API endpoints")
            print("- Обновить селекторы")
            print("- Увеличить время ожидания")
        
        if not results.get('admin_integration'):
            print("- Запустить админ панель")
            print("- Проверить сетевое подключение")

if __name__ == "__main__":
    main()
