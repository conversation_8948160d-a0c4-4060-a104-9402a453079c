#!/usr/bin/env python3
"""
Диагностический скрипт для тестирования поиска в админ панели Uzum Parser
"""

import requests
import time
import json
from datetime import datetime

def test_admin_panel_connection():
    """Тестирование подключения к админ панели"""
    print("🔌 Тестирование подключения к админ панели")
    print("-" * 50)
    
    try:
        response = requests.get("http://localhost:5000/api/status", timeout=5)
        if response.status_code == 200:
            status = response.json()
            print("✅ Админ панель доступна")
            print(f"📊 Статус: {status}")
            return True
        else:
            print(f"❌ Ошибка статуса: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Ошибка подключения: {e}")
        print("💡 Убедитесь, что админ панель запущена: python start_admin.py")
        return False

def test_search_api(query="триммер"):
    """Тестирование API поиска"""
    print(f"\n🔍 Тестирование поиска: '{query}'")
    print("-" * 50)
    
    try:
        # Запускаем поиск
        search_data = {
            "query": query,
            "max_pages": 1
        }
        
        print(f"📡 Отправка запроса поиска: {search_data}")
        response = requests.post(
            "http://localhost:5000/api/search",
            json=search_data,
            timeout=10
        )
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Поиск запущен: {result}")
            
            # Ждем завершения поиска
            print("⏳ Ожидание завершения поиска...")
            max_wait = 60  # максимум 60 секунд
            start_time = time.time()
            
            while time.time() - start_time < max_wait:
                status_response = requests.get("http://localhost:5000/api/status")
                if status_response.status_code == 200:
                    status = status_response.json()
                    
                    if not status.get('running', False):
                        print(f"✅ Поиск завершен за {time.time() - start_time:.1f} секунд")
                        print(f"📊 Найдено товаров: {status.get('total_products', 0)}")
                        print(f"❌ Ошибок: {status.get('errors', 0)}")
                        return True
                    else:
                        current_task = status.get('current_task', 'Неизвестно')
                        print(f"⏳ Выполняется: {current_task}")
                
                time.sleep(2)
            
            print("⏰ Превышено время ожидания")
            return False
            
        else:
            print(f"❌ Ошибка запуска поиска: {response.status_code}")
            print(f"📄 Ответ: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Ошибка API поиска: {e}")
        return False

def test_results_api():
    """Тестирование API результатов"""
    print("\n📋 Тестирование API результатов")
    print("-" * 50)
    
    try:
        response = requests.get("http://localhost:5000/api/results", timeout=10)
        
        if response.status_code == 200:
            results = response.json()
            print("✅ API результатов доступен")
            
            # Анализируем структуру результатов
            print(f"📊 Структура результатов:")
            print(f"   - main_page: {len(results.get('main_page', []))} товаров")
            print(f"   - search_results: {len(results.get('search_results', {}))} запросов")
            print(f"   - last_updated: {results.get('last_updated', 'Никогда')}")
            
            # Детальный анализ результатов поиска
            search_results = results.get('search_results', {})
            if search_results:
                print(f"\n🔍 Детали результатов поиска:")
                for query, products in search_results.items():
                    print(f"   📝 Запрос '{query}': {len(products)} товаров")
                    
                    if products:
                        # Анализируем первый товар
                        first_product = products[0]
                        print(f"      📦 Пример товара:")
                        print(f"         - ID: {first_product.get('product_id', 'Нет')}")
                        print(f"         - Название: {first_product.get('full_name', first_product.get('short_name', 'Нет'))}")
                        print(f"         - Цена: {first_product.get('price', 'Нет')}")
                        print(f"         - URL: {first_product.get('url', 'Нет')}")
                        
                        # Проверяем структуру данных
                        required_fields = ['product_id', 'url']
                        missing_fields = [field for field in required_fields if not first_product.get(field)]
                        
                        if missing_fields:
                            print(f"      ⚠️ Отсутствуют поля: {missing_fields}")
                        else:
                            print(f"      ✅ Структура данных корректна")
            
            return True, results
            
        else:
            print(f"❌ Ошибка API результатов: {response.status_code}")
            return False, None
            
    except Exception as e:
        print(f"❌ Ошибка API результатов: {e}")
        return False, None

def test_logs_api():
    """Тестирование API логов"""
    print("\n📝 Тестирование API логов")
    print("-" * 50)
    
    try:
        response = requests.get("http://localhost:5000/api/logs", timeout=10)
        
        if response.status_code == 200:
            logs = response.json()
            print(f"✅ API логов доступен. Получено {len(logs)} записей")
            
            # Показываем последние 5 записей
            if logs:
                print(f"\n📄 Последние 5 записей логов:")
                for i, log in enumerate(logs[:5]):
                    timestamp = log.get('timestamp', 'N/A')
                    level = log.get('level', 'N/A')
                    message = log.get('message', 'N/A')
                    
                    # Форматируем время
                    try:
                        dt = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
                        time_str = dt.strftime('%H:%M:%S')
                    except:
                        time_str = timestamp[:8] if len(timestamp) > 8 else timestamp
                    
                    print(f"   {i+1}. [{time_str}] {level}: {message}")
            
            return True, logs
            
        else:
            print(f"❌ Ошибка API логов: {response.status_code}")
            return False, None
            
    except Exception as e:
        print(f"❌ Ошибка API логов: {e}")
        return False, None

def analyze_search_problem(results_data, logs_data):
    """Анализ проблем с поиском"""
    print("\n🔍 АНАЛИЗ ПРОБЛЕМ С ПОИСКОМ")
    print("=" * 50)
    
    issues = []
    
    # Проверка 1: Есть ли результаты поиска
    search_results = results_data.get('search_results', {}) if results_data else {}
    
    if not search_results:
        issues.append("❌ Нет результатов поиска в API")
    else:
        print(f"✅ Найдено {len(search_results)} поисковых запросов")
        
        # Проверяем каждый запрос
        for query, products in search_results.items():
            if not products:
                issues.append(f"❌ Запрос '{query}' не вернул товаров")
            else:
                print(f"✅ Запрос '{query}': {len(products)} товаров")
                
                # Проверяем структуру данных
                for i, product in enumerate(products[:3]):  # Проверяем первые 3
                    required_fields = ['product_id', 'url']
                    missing = [f for f in required_fields if not product.get(f)]
                    
                    if missing:
                        issues.append(f"❌ Товар {i+1} в '{query}': отсутствуют поля {missing}")
    
    # Проверка 2: Анализ логов
    if logs_data:
        search_logs = [log for log in logs_data if 'поиск' in log.get('message', '').lower()]
        error_logs = [log for log in logs_data if log.get('level') == 'ERROR']
        
        print(f"📝 Логов о поиске: {len(search_logs)}")
        print(f"❌ Логов с ошибками: {len(error_logs)}")
        
        if error_logs:
            print(f"\n🚨 Последние ошибки:")
            for error in error_logs[-3:]:  # Последние 3 ошибки
                print(f"   - {error.get('message', 'Неизвестная ошибка')}")
                issues.append(f"❌ Ошибка в логах: {error.get('message', 'Неизвестная ошибка')}")
    
    # Итоги
    if not issues:
        print("\n🎉 Проблем не обнаружено! Поиск работает корректно.")
        return True
    else:
        print(f"\n⚠️ Обнаружено {len(issues)} проблем:")
        for i, issue in enumerate(issues, 1):
            print(f"   {i}. {issue}")
        
        print(f"\n🔧 РЕКОМЕНДАЦИИ:")
        
        if any("Нет результатов поиска" in issue for issue in issues):
            print("   - Проверьте функцию get_searchpage_cards() в uzum_parser.py")
            print("   - Убедитесь, что results_cache обновляется корректно")
        
        if any("не вернул товаров" in issue for issue in issues):
            print("   - Проверьте селекторы для поиска товаров")
            print("   - Убедитесь, что сайт uzum.uz доступен")
        
        if any("отсутствуют поля" in issue for issue in issues):
            print("   - Проверьте функцию extract_card_info()")
            print("   - Обновите селекторы для извлечения данных товаров")
        
        if any("Ошибка в логах" in issue for issue in issues):
            print("   - Проверьте детальные логи для диагностики ошибок")
            print("   - Возможно, нужно обновить ChromeDriver или селекторы")
        
        return False

def main():
    """Основная функция диагностики"""
    print("🔍 ДИАГНОСТИКА ПОИСКА UZUM PARSER")
    print("=" * 60)
    print(f"⏰ Время: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Тест 1: Подключение к админ панели
    if not test_admin_panel_connection():
        print("\n❌ Не удалось подключиться к админ панели. Завершение.")
        return
    
    # Тест 2: Запуск поиска
    search_success = test_search_api("триммер")
    
    # Тест 3: Проверка результатов
    results_success, results_data = test_results_api()
    
    # Тест 4: Проверка логов
    logs_success, logs_data = test_logs_api()
    
    # Анализ проблем
    if results_success and logs_success:
        analyze_search_problem(results_data, logs_data)
    
    print(f"\n📊 ИТОГИ ТЕСТИРОВАНИЯ:")
    print(f"   🔍 Поиск API: {'✅' if search_success else '❌'}")
    print(f"   📋 Результаты API: {'✅' if results_success else '❌'}")
    print(f"   📝 Логи API: {'✅' if logs_success else '❌'}")

if __name__ == "__main__":
    main()
