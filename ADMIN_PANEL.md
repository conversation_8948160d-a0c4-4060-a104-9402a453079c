# Админ панель Uzum Parser

Веб-интерфейс для управления и мониторинга парсера Uzum marketplace.

## 🚀 Быстрый старт

### Запуск админ панели

```bash
python start_admin.py
```

Или напрямую:

```bash
python admin_panel.py
```

После запуска откройте браузер и перейдите по адресу: **http://localhost:5000**

## 📊 Возможности

### 1. Мониторинг статуса
- **Статус парсера**: Показывает, работает ли парсер в данный момент
- **Количество товаров**: Общее количество найденных товаров
- **Ошибки**: Счетчик ошибок во время парсинга
- **Последний запуск**: Время последнего выполнения парсинга
- **Текущая задача**: Отображает выполняемую в данный момент операцию

### 2. Тестирование товаров
- Проверка парсинга конкретного товара по URL
- Отображение всей извлеченной информации
- Проверка работоспособности парсера

**Пример использования:**
- Введите URL: `/ru/product/example-product-123`
- Нажмите "Проверить товар"
- Просмотрите результат на вкладке "Тест товара"

### 3. Поиск товаров
- Поиск товаров по ключевым словам
- Настройка количества страниц для парсинга
- Асинхронное выполнение с отображением прогресса

**Параметры:**
- **Поисковый запрос**: Ключевые слова для поиска
- **Максимум страниц**: От 1 до 5 страниц результатов

### 4. Парсинг главной страницы
- Извлечение товаров с главной страницы uzum.uz
- Автоматическая прокрутка для загрузки динамического контента
- Сохранение результатов для просмотра

### 5. Управление данными
- **Экспорт результатов**: Скачивание всех данных в JSON формате
- **Очистка результатов**: Удаление всех сохраненных данных
- Автоматическое создание резервных копий

## 🎛️ Интерфейс

### Панель статуса
Отображает текущее состояние парсера в реальном времени:
- 🟢 **Активен** - парсер выполняет задачу
- 🔴 **Неактивен** - парсер готов к работе
- ⚠️ **Ошибка** - возникли проблемы

### Панели управления

#### 🔍 Тест товара
- Поле ввода URL товара
- Кнопка "Проверить товар"
- Результат отображается на отдельной вкладке

#### 🔎 Поиск товаров
- Поле ввода поискового запроса
- Выбор количества страниц
- Кнопка "Начать поиск"

#### 🏠 Главная страница
- Кнопка "Парсить главную"
- Автоматический запуск парсинга

#### 💾 Управление данными
- Кнопка "Экспорт результатов"
- Кнопка "Очистить результаты"

### Вкладки результатов

#### Главная страница
Отображает товары, найденные на главной странице uzum.uz

#### Результаты поиска
Показывает товары по каждому поисковому запросу

#### Тест товара
Детальная информация о протестированном товаре

## 🔧 API Endpoints

### GET /api/status
Получение текущего статуса парсера

**Ответ:**
```json
{
  "running": false,
  "last_run": "2024-01-15T10:30:00",
  "total_products": 25,
  "errors": 0,
  "current_task": null
}
```

### POST /api/test-product
Тестирование парсинга товара

**Запрос:**
```json
{
  "url": "/ru/product/example-123"
}
```

**Ответ:**
```json
{
  "success": true,
  "product": {
    "product_id": "example-123",
    "title": "Название товара",
    "price": "1000 сум",
    "rating": "4.5",
    "description": "Описание товара..."
  },
  "timestamp": "2024-01-15T10:30:00"
}
```

### POST /api/search
Запуск поиска товаров

**Запрос:**
```json
{
  "query": "телефон",
  "max_pages": 2
}
```

### POST /api/parse-main
Запуск парсинга главной страницы

### GET /api/results
Получение всех результатов парсинга

### GET /api/export
Экспорт результатов в JSON файл

### POST /api/clear-results
Очистка всех результатов

## 📁 Структура файлов

```
uzum-parser/
├── admin_panel.py          # Основной файл Flask приложения
├── start_admin.py          # Скрипт запуска
├── templates/
│   └── admin.html          # HTML шаблон интерфейса
├── exports/                # Папка для экспортированных файлов
├── logs/                   # Папка для логов
├── get_uzum_product_info.py
├── uzum_parser.py
└── requirements.txt
```

## 🛠️ Технические детали

### Технологии
- **Backend**: Flask (Python)
- **Frontend**: HTML, CSS, JavaScript
- **Парсинг**: Selenium, BeautifulSoup, curl_cffi
- **Стили**: Современный responsive дизайн

### Особенности
- **Асинхронность**: Парсинг выполняется в отдельных потоках
- **Реальное время**: Автоматическое обновление статуса каждые 2 секунды
- **Responsive**: Адаптивный дизайн для мобильных устройств
- **Безопасность**: CORS настроен для локального использования

### Производительность
- Легковесный интерфейс
- Минимальное использование ресурсов
- Эффективное кэширование результатов

## 🔒 Безопасность

### Рекомендации
- Используйте только в локальной сети
- Не открывайте порт 5000 для внешнего доступа
- Регулярно очищайте результаты для экономии места

### Ограничения
- Админ панель предназначена для локального использования
- Нет системы аутентификации
- Все операции выполняются с правами текущего пользователя

## 🐛 Устранение неполадок

### Частые проблемы

#### Порт 5000 занят
```bash
# Найти процесс, использующий порт
netstat -ano | findstr :5000

# Завершить процесс (замените PID)
taskkill /PID <PID> /F
```

#### Ошибки импорта
```bash
# Переустановить зависимости
pip install -r requirements.txt --force-reinstall
```

#### Chrome/ChromeDriver проблемы
- Убедитесь, что Chrome установлен
- Обновите ChromeDriver до совместимой версии
- Проверьте PATH для ChromeDriver

#### Ошибки доступа к uzum.uz
- Проверьте интернет-соединение
- Убедитесь, что сайт доступен
- Попробуйте изменить User-Agent

### Логи
Логи сохраняются в папке `logs/` (функция в разработке)

## 📈 Мониторинг

### Метрики
- Количество обработанных товаров
- Время выполнения операций
- Количество ошибок
- Статус выполнения задач

### Производительность
- Среднее время парсинга одного товара: ~2-3 секунды
- Рекомендуемое количество страниц поиска: 1-3
- Максимальное количество товаров за сессию: 100-200

## 🔄 Обновления

### Планируемые функции
- [ ] Система логирования
- [ ] Планировщик задач
- [ ] Уведомления о завершении
- [ ] Статистика по времени
- [ ] Фильтры результатов
- [ ] Экспорт в CSV/Excel

### История версий
- **v1.0** - Базовая функциональность
- **v1.1** - Улучшенный интерфейс
- **v1.2** - Добавлен экспорт данных

## 📞 Поддержка

При возникновении проблем:
1. Проверьте логи в консоли браузера (F12)
2. Убедитесь, что все зависимости установлены
3. Перезапустите админ панель
4. Проверьте доступность uzum.uz

## 📄 Лицензия

MIT License - свободное использование и модификация.
