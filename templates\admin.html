<!DOCTYPE html>
<html lang="ru">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>U<PERSON><PERSON>rser - Админ Панель</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .main-content {
            padding: 30px;
        }

        .status-panel {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 30px;
            border-left: 5px solid #28a745;
        }

        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 15px;
        }

        .status-item {
            text-align: center;
            padding: 15px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .status-value {
            font-size: 2em;
            font-weight: bold;
            color: #667eea;
        }

        .status-label {
            color: #666;
            margin-top: 5px;
        }

        .controls {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin-bottom: 30px;
        }

        .control-panel {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 25px;
        }

        .control-panel h3 {
            color: #333;
            margin-bottom: 20px;
            font-size: 1.3em;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            color: #555;
            font-weight: 500;
        }

        .form-group input,
        .form-group select {
            width: 100%;
            padding: 12px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s;
        }

        .form-group input:focus,
        .form-group select:focus {
            outline: none;
            border-color: #667eea;
        }

        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: transform 0.2s, box-shadow 0.2s;
            width: 100%;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .btn:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .btn-secondary {
            background: #6c757d;
        }

        .btn-danger {
            background: #dc3545;
        }

        .results-panel {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 25px;
            margin-top: 30px;
        }

        .results-panel h3 {
            color: #333;
            margin-bottom: 20px;
        }

        .product-card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 15px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            border-left: 4px solid #667eea;
        }

        .product-title {
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }

        .product-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 10px;
            font-size: 14px;
            color: #666;
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: #667eea;
        }

        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }

        @keyframes spin {
            0% {
                transform: rotate(0deg);
            }

            100% {
                transform: rotate(360deg);
            }
        }

        .alert {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }

        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .tabs {
            display: flex;
            margin-bottom: 20px;
            border-bottom: 2px solid #e0e0e0;
        }

        .tab {
            padding: 12px 20px;
            cursor: pointer;
            border-bottom: 2px solid transparent;
            transition: all 0.3s;
        }

        .tab.active {
            border-bottom-color: #667eea;
            color: #667eea;
            font-weight: 500;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        .log-entry {
            margin-bottom: 8px;
            padding: 8px 12px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            border-left: 3px solid #ddd;
        }

        .log-entry.INFO {
            background: #e8f5e8;
            border-left-color: #28a745;
            color: #155724;
        }

        .log-entry.ERROR {
            background: #f8e8e8;
            border-left-color: #dc3545;
            color: #721c24;
        }

        .log-entry.WARNING {
            background: #fff3cd;
            border-left-color: #ffc107;
            color: #856404;
        }

        .log-entry.DEBUG {
            background: #e2e3e5;
            border-left-color: #6c757d;
            color: #383d41;
        }

        .log-timestamp {
            color: #666;
            font-size: 11px;
            margin-right: 10px;
        }

        .log-level {
            font-weight: bold;
            margin-right: 10px;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 10px;
        }

        .log-level.INFO {
            background: #28a745;
            color: white;
        }

        .log-level.ERROR {
            background: #dc3545;
            color: white;
        }

        .log-level.WARNING {
            background: #ffc107;
            color: #212529;
        }

        .log-level.DEBUG {
            background: #6c757d;
            color: white;
        }

        @media (max-width: 768px) {
            .controls {
                grid-template-columns: 1fr;
            }

            .status-grid {
                grid-template-columns: repeat(2, 1fr);
            }

            .header h1 {
                font-size: 2em;
            }
        }
    </style>
</head>

<body>
    <div class="container">
        <div class="header">
            <h1>🛍️ Uzum Parser</h1>
            <p>Админ панель для управления парсером маркетплейса</p>
        </div>

        <div class="main-content">
            <!-- Панель статуса -->
            <div class="status-panel">
                <h3>📊 Статус парсера</h3>
                <div class="status-grid">
                    <div class="status-item">
                        <div class="status-value" id="status-running">⏸️</div>
                        <div class="status-label">Статус</div>
                    </div>
                    <div class="status-item">
                        <div class="status-value" id="status-products">0</div>
                        <div class="status-label">Товаров найдено</div>
                    </div>
                    <div class="status-item">
                        <div class="status-value" id="status-errors">0</div>
                        <div class="status-label">Ошибок</div>
                    </div>
                    <div class="status-item">
                        <div class="status-value" id="status-last-run">Никогда</div>
                        <div class="status-label">Последний запуск</div>
                    </div>
                </div>
                <div id="current-task" style="margin-top: 15px; font-style: italic; color: #666;"></div>
            </div>

            <!-- Панели управления -->
            <div class="controls">
                <!-- Тест одного товара -->
                <div class="control-panel">
                    <h3>🔍 Тест товара</h3>
                    <div class="form-group">
                        <label for="product-url">URL товара:</label>
                        <input type="text" id="product-url" placeholder="/ru/product/example-123" />
                    </div>
                    <button class="btn" onclick="testProduct()">Проверить товар</button>
                </div>

                <!-- Поиск товаров -->
                <div class="control-panel">
                    <h3>🔎 Поиск товаров</h3>
                    <div class="form-group">
                        <label for="search-query">Поисковый запрос:</label>
                        <input type="text" id="search-query" placeholder="телефон, ноутбук, одежда..." />
                    </div>
                    <div class="form-group">
                        <label for="max-pages">Максимум страниц:</label>
                        <select id="max-pages">
                            <option value="1">1 страница</option>
                            <option value="2">2 страницы</option>
                            <option value="3">3 страницы</option>
                            <option value="5">5 страниц</option>
                        </select>
                    </div>
                    <button class="btn" onclick="searchProducts()">Начать поиск</button>
                </div>

                <!-- Парсинг главной страницы -->
                <div class="control-panel">
                    <h3>🏠 Главная страница</h3>
                    <p style="margin-bottom: 20px; color: #666;">Парсинг товаров с главной страницы uzum.uz</p>
                    <button class="btn" onclick="parseMainPage()">Парсить главную</button>
                </div>

                <!-- Управление данными -->
                <div class="control-panel">
                    <h3>💾 Управление данными</h3>
                    <button class="btn btn-secondary" onclick="exportResults()" style="margin-bottom: 10px;">Экспорт
                        результатов</button>
                    <button class="btn btn-danger" onclick="clearResults()">Очистить результаты</button>
                </div>
            </div>

            <!-- Результаты -->
            <div class="results-panel">
                <h3>📋 Результаты парсинга</h3>

                <div class="tabs">
                    <div class="tab active" onclick="showTab('main-page')">Главная страница</div>
                    <div class="tab" onclick="showTab('search-results')">Результаты поиска</div>
                    <div class="tab" onclick="showTab('test-result')">Тест товара</div>
                    <div class="tab" onclick="showTab('logs')">Логи</div>
                </div>

                <div id="main-page" class="tab-content active">
                    <div id="main-page-results">
                        <p style="color: #666; text-align: center; padding: 40px;">
                            Нажмите "Парсить главную" для получения товаров с главной страницы
                        </p>
                    </div>
                </div>

                <div id="search-results" class="tab-content">
                    <div id="search-results-content">
                        <p style="color: #666; text-align: center; padding: 40px;">
                            Выполните поиск для просмотра результатов
                        </p>
                    </div>
                </div>

                <div id="test-result" class="tab-content">
                    <div id="test-result-content">
                        <p style="color: #666; text-align: center; padding: 40px;">
                            Протестируйте товар для просмотра результата
                        </p>
                    </div>
                </div>

                <div id="logs" class="tab-content">
                    <div style="margin-bottom: 15px;">
                        <button class="btn btn-secondary" onclick="updateLogs()"
                            style="width: auto; margin-right: 10px;">🔄 Обновить логи</button>
                        <button class="btn btn-secondary" onclick="clearLogs()" style="width: auto;">🗑️ Очистить
                            логи</button>
                    </div>
                    <div id="logs-content"
                        style="max-height: 400px; overflow-y: auto; background: #f8f9fa; border-radius: 8px; padding: 15px;">
                        <p style="color: #666; text-align: center; padding: 20px;">
                            Загрузка логов...
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Глобальные переменные
        let updateInterval;

        // Инициализация
        document.addEventListener('DOMContentLoaded', function () {
            updateStatus();
            updateResults();
            updateLogs();

            // Обновляем статус каждые 2 секунды
            updateInterval = setInterval(updateStatus, 2000);

            // Обновляем логи каждые 5 секунд
            setInterval(updateLogs, 5000);
        });

        // Обновление статуса
        async function updateStatus() {
            try {
                const response = await fetch('/api/status');
                const status = await response.json();

                document.getElementById('status-running').textContent = status.running ? '▶️' : '⏸️';
                document.getElementById('status-products').textContent = status.total_products;
                document.getElementById('status-errors').textContent = status.errors;
                document.getElementById('status-last-run').textContent =
                    status.last_run ? new Date(status.last_run).toLocaleString() : 'Никогда';

                const taskElement = document.getElementById('current-task');
                if (status.current_task) {
                    taskElement.textContent = `Выполняется: ${status.current_task}`;
                    taskElement.style.display = 'block';
                } else {
                    taskElement.style.display = 'none';
                }

                // Обновляем кнопки в зависимости от статуса
                const buttons = document.querySelectorAll('.btn');
                buttons.forEach(btn => {
                    btn.disabled = status.running;
                });

            } catch (error) {
                console.error('Ошибка обновления статуса:', error);
            }
        }

        // Обновление результатов
        async function updateResults() {
            try {
                const response = await fetch('/api/results');
                const results = await response.json();

                // Обновляем результаты главной страницы
                displayMainPageResults(results.main_page);

                // Обновляем результаты поиска
                displaySearchResults(results.search_results);

            } catch (error) {
                console.error('Ошибка обновления результатов:', error);
            }
        }

        // Отображение результатов главной страницы
        function displayMainPageResults(products) {
            const container = document.getElementById('main-page-results');

            if (!products || products.length === 0) {
                container.innerHTML = '<p style="color: #666; text-align: center; padding: 40px;">Товары не найдены</p>';
                return;
            }

            container.innerHTML = products.map(product => `
                <div class="product-card">
                    <div class="product-title">${product.full_name || product.short_name || 'Без названия'}</div>
                    <div class="product-info">
                        <div><strong>ID:</strong> ${product.product_id || 'N/A'}</div>
                        <div><strong>Цена:</strong> ${product.price || 'N/A'}</div>
                        <div><strong>Рейтинг:</strong> ${product.rating || 'N/A'}</div>
                        <div><strong>URL:</strong> <a href="${product.url}" target="_blank">Открыть</a></div>
                    </div>
                </div>
            `).join('');
        }

        // Отображение результатов поиска
        function displaySearchResults(searchResults) {
            const container = document.getElementById('search-results-content');

            if (!searchResults || Object.keys(searchResults).length === 0) {
                container.innerHTML = '<p style="color: #666; text-align: center; padding: 40px;">Результаты поиска отсутствуют</p>';
                return;
            }

            let html = '';
            for (const [query, products] of Object.entries(searchResults)) {
                html += `<h4>Поиск: "${query}" (${products.length} товаров)</h4>`;
                html += products.map(product => `
                    <div class="product-card">
                        <div class="product-title">${product.full_name || product.short_name || 'Без названия'}</div>
                        <div class="product-info">
                            <div><strong>ID:</strong> ${product.product_id || 'N/A'}</div>
                            <div><strong>Цена:</strong> ${product.price || 'N/A'}</div>
                            <div><strong>Рейтинг:</strong> ${product.rating || 'N/A'}</div>
                            <div><strong>URL:</strong> <a href="${product.url}" target="_blank">Открыть</a></div>
                        </div>
                    </div>
                `).join('');
            }

            container.innerHTML = html;
        }

        // Тест товара
        async function testProduct() {
            const url = document.getElementById('product-url').value.trim();

            if (!url) {
                alert('Введите URL товара');
                return;
            }

            const container = document.getElementById('test-result-content');
            container.innerHTML = '<div class="loading"><div class="spinner"></div>Тестируем товар...</div>';

            showTab('test-result');

            try {
                const response = await fetch('/api/test-product', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ url: url })
                });

                const result = await response.json();

                if (result.success) {
                    const product = result.product;
                    container.innerHTML = `
                        <div class="alert alert-success">✅ Товар успешно обработан</div>
                        <div class="product-card">
                            <div class="product-title">${product.title || 'Без названия'}</div>
                            <div class="product-info">
                                <div><strong>ID:</strong> ${product.product_id || 'N/A'}</div>
                                <div><strong>Цена:</strong> ${product.price || 'N/A'}</div>
                                <div><strong>Рейтинг:</strong> ${product.rating || 'N/A'}</div>
                                <div><strong>Бренд:</strong> ${product.brand || 'N/A'}</div>
                                <div><strong>Описание:</strong> ${product.description ? product.description.substring(0, 200) + '...' : 'N/A'}</div>
                                <div><strong>Изображение:</strong> ${product.image_url ? '<a href="' + product.image_url + '" target="_blank">Открыть</a>' : 'N/A'}</div>
                            </div>
                        </div>
                    `;
                } else {
                    container.innerHTML = `<div class="alert alert-error">❌ ${result.error}</div>`;
                }

            } catch (error) {
                container.innerHTML = `<div class="alert alert-error">❌ Ошибка: ${error.message}</div>`;
            }
        }

        // Поиск товаров
        async function searchProducts() {
            const query = document.getElementById('search-query').value.trim();
            const maxPages = document.getElementById('max-pages').value;

            if (!query) {
                alert('Введите поисковый запрос');
                return;
            }

            try {
                const response = await fetch('/api/search', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        query: query,
                        max_pages: parseInt(maxPages)
                    })
                });

                const result = await response.json();
                alert(result.message);

                // Переключаемся на вкладку результатов поиска
                showTab('search-results');

                // Обновляем результаты через 5 секунд
                setTimeout(updateResults, 5000);

            } catch (error) {
                alert('Ошибка запуска поиска: ' + error.message);
            }
        }

        // Парсинг главной страницы
        async function parseMainPage() {
            try {
                const response = await fetch('/api/parse-main', {
                    method: 'POST'
                });

                const result = await response.json();
                alert(result.message);

                // Переключаемся на вкладку главной страницы
                showTab('main-page');

                // Обновляем результаты через 10 секунд
                setTimeout(updateResults, 10000);

            } catch (error) {
                alert('Ошибка запуска парсинга: ' + error.message);
            }
        }

        // Экспорт результатов
        async function exportResults() {
            try {
                const response = await fetch('/api/export');

                if (response.ok) {
                    const blob = await response.blob();
                    const url = window.URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = `uzum_export_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.json`;
                    document.body.appendChild(a);
                    a.click();
                    window.URL.revokeObjectURL(url);
                    document.body.removeChild(a);

                    alert('Результаты экспортированы');
                } else {
                    alert('Ошибка экспорта');
                }

            } catch (error) {
                alert('Ошибка экспорта: ' + error.message);
            }
        }

        // Очистка результатов
        async function clearResults() {
            if (!confirm('Вы уверены, что хотите очистить все результаты?')) {
                return;
            }

            try {
                const response = await fetch('/api/clear-results', {
                    method: 'POST'
                });

                const result = await response.json();
                alert(result.message);

                updateResults();

            } catch (error) {
                alert('Ошибка очистки: ' + error.message);
            }
        }

        // Переключение вкладок
        function showTab(tabName) {
            // Скрываем все вкладки
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.classList.remove('active');
            });

            document.querySelectorAll('.tab').forEach(tab => {
                tab.classList.remove('active');
            });

            // Показываем выбранную вкладку
            document.getElementById(tabName).classList.add('active');

            // Активируем соответствующую кнопку
            event.target.classList.add('active');

            // Если открываем вкладку логов, обновляем их
            if (tabName === 'logs') {
                updateLogs();
            }
        }

        // Обновление логов
        async function updateLogs() {
            try {
                const response = await fetch('/api/logs');
                const logs = await response.json();

                displayLogs(logs);

            } catch (error) {
                console.error('Ошибка обновления логов:', error);
                document.getElementById('logs-content').innerHTML =
                    '<div class="alert alert-error">❌ Ошибка загрузки логов</div>';
            }
        }

        // Отображение логов
        function displayLogs(logs) {
            const container = document.getElementById('logs-content');

            if (!logs || logs.length === 0) {
                container.innerHTML = '<p style="color: #666; text-align: center; padding: 20px;">Логи отсутствуют</p>';
                return;
            }

            const logsHtml = logs.map(log => {
                const timestamp = new Date(log.timestamp).toLocaleString();
                return `
                    <div class="log-entry ${log.level}">
                        <span class="log-timestamp">${timestamp}</span>
                        <span class="log-level ${log.level}">${log.level}</span>
                        <span class="log-message">${log.message}</span>
                    </div>
                `;
            }).join('');

            container.innerHTML = logsHtml;

            // Прокручиваем к последнему логу
            container.scrollTop = container.scrollHeight;
        }

        // Очистка логов
        async function clearLogs() {
            if (!confirm('Очистить логи в памяти? (файлы логов останутся)')) {
                return;
            }

            try {
                const response = await fetch('/api/clear-logs', {
                    method: 'POST'
                });

                const result = await response.json();
                alert(result.message);

                // Обновляем отображение логов
                updateLogs();

            } catch (error) {
                alert('Ошибка очистки логов: ' + error.message);
            }
        }
    </script>
</body>

</html>