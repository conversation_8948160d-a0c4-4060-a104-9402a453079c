import time
import json
import re
from urllib.parse import urljoin, urlparse

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from selenium_stealth import stealth
from bs4 import BeautifulSoup
from webdriver_manager.chrome import ChromeDriverManager
from selenium.webdriver.chrome.service import Service

from curl_cffi import requests

def init_webdriver():
    """Инициализация Chrome драйвера с stealth настройками"""
    options = webdriver.ChromeOptions()
    options.add_argument('--no-sandbox')
    options.add_argument('--disable-dev-shm-usage')
    options.add_argument('--disable-blink-features=AutomationControlled')
    options.add_experimental_option("excludeSwitches", ["enable-automation"])
    options.add_experimental_option('useAutomationExtension', False)

    # Автоматическая установка ChromeDriver
    try:
        service = Service(ChromeDriverManager().install())
        driver = webdriver.Chrome(service=service, options=options)
        print("✅ ChromeDriver автоматически установлен и настроен")
    except Exception as e:
        print(f"⚠️ Ошибка автоматической установки ChromeDriver: {e}")
        print("💡 Попытка использовать системный ChromeDriver...")
        driver = webdriver.Chrome(options=options)

    stealth(driver,
            languages=["ru-RU", "ru", "en-US", "en"],
            vendor="Google Inc.",
            platform="Win32",
            webgl_vendor="Intel Inc.",
            renderer="Intel Iris OpenGL Engine",
            fix_hairline=True)
    driver.maximize_window()
    return driver

def scrolldown(driver, deep):
    """Прокрутка страницы для загрузки динамического контента"""
    for _ in range(deep):
        driver.execute_script('window.scrollBy(0, 500)')
        time.sleep(0.2)

def get_product_info(product_url, session=None):
    """Получение детальной информации о товаре"""
    if session is None:
        session = requests.Session()
    
    try:
        # Попробуем получить информацию через API или прямой запрос
        full_url = f"https://uzum.uz{product_url}" if not product_url.startswith('http') else product_url
        
        response = session.get(full_url, timeout=10)
        soup = BeautifulSoup(response.content, 'html.parser')
        
        # Извлекаем информацию из HTML
        product_id = extract_product_id(product_url)
        
        # Название товара
        title_element = soup.find('h1') or soup.find('title')
        full_name = title_element.get_text(strip=True) if title_element else "Название не найдено"
        
        # Описание
        description_element = soup.find('meta', {'name': 'description'})
        description = description_element.get('content', '') if description_element else ""
        
        # Цена
        price = extract_price(soup)
        
        # Рейтинг
        rating = extract_rating(soup)
        
        # Изображение
        image_url = extract_image_url(soup)
        
        return {
            'product_id': product_id,
            'full_name': full_name,
            'description': description,
            'price': price,
            'rating': rating,
            'image_url': image_url,
            'url': full_url
        }
        
    except Exception as e:
        print(f"Ошибка при получении информации о товаре {product_url}: {e}")
        return None

def extract_product_id(product_url):
    """Извлечение ID товара из URL"""
    # Ищем числовой ID в URL
    match = re.search(r'/(\d+)/?$', product_url)
    if match:
        return match.group(1)
    
    # Если не найден, используем последнюю часть URL
    return product_url.split('/')[-1] or product_url.split('/')[-2]

def extract_price(soup):
    """Извлечение цены товара"""
    price_selectors = [
        '[data-testid="price-current"]',
        '.price',
        '[class*="price"]',
        '[data-price]'
    ]
    
    for selector in price_selectors:
        price_element = soup.select_one(selector)
        if price_element:
            price_text = price_element.get_text(strip=True)
            # Очищаем от лишних символов, оставляем только цифры и валюту
            price_clean = re.sub(r'[^\d\s.,сум]', '', price_text)
            return price_clean.strip()
    
    return "Цена не найдена"

def extract_rating(soup):
    """Извлечение рейтинга товара"""
    rating_selectors = [
        '[data-testid="rating"]',
        '.rating',
        '[class*="rating"]',
        '[class*="star"]'
    ]
    
    for selector in rating_selectors:
        rating_element = soup.select_one(selector)
        if rating_element:
            rating_text = rating_element.get_text(strip=True)
            # Ищем числовое значение рейтинга
            match = re.search(r'(\d+[.,]\d+|\d+)', rating_text)
            if match:
                return match.group(1)
    
    return "Рейтинг не найден"

def extract_image_url(soup):
    """Извлечение URL изображения товара"""
    image_selectors = [
        'meta[property="og:image"]',
        '[data-testid="product-image"] img',
        '.product-image img',
        'img[alt*="товар"]',
        'img[alt*="product"]'
    ]
    
    for selector in image_selectors:
        img_element = soup.select_one(selector)
        if img_element:
            if selector.startswith('meta'):
                return img_element.get('content', '')
            else:
                return img_element.get('src', '') or img_element.get('data-src', '')
    
    return ""

def get_mainpage_cards(driver, url):
    """Парсинг карточек товаров с главной страницы"""
    driver.get(url)
    time.sleep(3)
    
    # Прокручиваем страницу для загрузки товаров
    scrolldown(driver, 30)
    
    soup = BeautifulSoup(driver.page_source, "html.parser")
    
    # Ищем карточки товаров на главной странице
    product_cards = find_product_cards(soup)
    
    all_cards = []
    session = requests.Session()
    
    for card in product_cards[:20]:  # Ограничиваем количество для тестирования
        try:
            card_info = extract_card_info(card, session)
            if card_info:
                all_cards.append(card_info)
                print(f"{card_info['product_id']} - DONE")
        except Exception as e:
            print(f"Ошибка при обработке карточки: {e}")
            continue
    
    return all_cards

def find_product_cards(soup):
    """Поиск карточек товаров на странице"""
    # Различные селекторы для поиска карточек товаров
    card_selectors = [
        '[data-testid="product-card"]',
        '.product-card',
        '[class*="product"]',
        'a[href*="/product/"]',
        'a[href*="/goods/"]'
    ]
    
    for selector in card_selectors:
        cards = soup.select(selector)
        if cards:
            print(f"Найдено {len(cards)} карточек с селектором: {selector}")
            return cards
    
    # Если специфичные селекторы не работают, ищем ссылки на товары
    all_links = soup.find_all('a', href=True)
    product_links = [link for link in all_links if '/product/' in link.get('href', '') or '/goods/' in link.get('href', '')]
    
    print(f"Найдено {len(product_links)} ссылок на товары")
    return product_links

def extract_card_info(card, session):
    """Извлечение информации из карточки товара"""
    try:
        # Получаем ссылку на товар
        if card.name == 'a':
            product_url = card.get('href', '')
        else:
            link_element = card.find('a', href=True)
            product_url = link_element.get('href', '') if link_element else ''
        
        if not product_url:
            return None
        
        # Получаем краткое название из карточки
        title_element = card.find(['h3', 'h4', 'span', 'div'], string=True)
        short_name = title_element.get_text(strip=True) if title_element else "Название не найдено"
        
        # Получаем детальную информацию о товаре
        product_info = get_product_info(product_url, session)
        
        if product_info:
            product_info['short_name'] = short_name
            return product_info
        
        return None
        
    except Exception as e:
        print(f"Ошибка при извлечении информации из карточки: {e}")
        return None

def get_searchpage_cards(driver, search_query, max_pages=3):
    """Парсинг карточек товаров со страниц поиска"""
    search_url = f"https://uzum.uz/ru/search?q={search_query}"
    
    all_cards = []
    session = requests.Session()
    
    for page in range(1, max_pages + 1):
        try:
            page_url = f"{search_url}&page={page}" if page > 1 else search_url
            driver.get(page_url)
            time.sleep(3)
            
            # Прокручиваем страницу
            scrolldown(driver, 20)
            
            soup = BeautifulSoup(driver.page_source, "html.parser")
            product_cards = find_product_cards(soup)
            
            if not product_cards:
                print(f"На странице {page} товары не найдены")
                break
            
            page_cards = []
            for card in product_cards[:10]:  # Ограничиваем количество на странице
                try:
                    card_info = extract_card_info(card, session)
                    if card_info:
                        page_cards.append(card_info)
                        print(f"{card_info['product_id']} - DONE (страница {page})")
                except Exception as e:
                    print(f"Ошибка при обработке карточки на странице {page}: {e}")
                    continue
            
            all_cards.extend(page_cards)
            
            if not page_cards:
                print(f"На странице {page} не удалось извлечь информацию о товарах")
                break
                
        except Exception as e:
            print(f"Ошибка при обработке страницы {page}: {e}")
            break
    
    return all_cards

if __name__ == "__main__":
    url_uzum = "https://uzum.uz/ru"
    
    driver = init_webdriver()
    
    search_list = ["телефон", "ноутбук", "одежда", "обувь"]
    results = {}
    
    try:
        # Парсинг главной страницы
        print("Начинаем парсинг главной страницы...")
        main_cards = get_mainpage_cards(driver, url_uzum)
        results['main_page'] = main_cards
        print(f"Успешно найдено {len(main_cards)} товаров на главной странице")
        
    except Exception as e:
        print(f"Ошибка при парсинге главной страницы: {e}")
        results['main_page'] = []
    
    # Парсинг страниц поиска
    for search_query in search_list:
        try:
            print(f"\nНачинаем поиск по запросу: {search_query}")
            search_cards = get_searchpage_cards(driver, search_query, max_pages=2)
            results[search_query] = search_cards
            print(f"Успешно найдено {len(search_cards)} товаров по запросу '{search_query}'")
            
        except Exception as e:
            print(f"Ошибка при поиске '{search_query}': {e}")
            results[search_query] = []
    
    # Сохраняем результаты в JSON файл
    with open('uzum_results.json', 'w', encoding='utf-8') as f:
        json.dump(results, f, ensure_ascii=False, indent=2)
    
    print(f"\nРезультаты сохранены в uzum_results.json")
    print(f"Общее количество найденных товаров: {sum(len(cards) for cards in results.values())}")
    
    driver.quit()
