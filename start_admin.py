#!/usr/bin/env python3
"""
Скрипт запуска админ панели Uzum Parser
"""

import os
import sys
import webbrowser
import time
from threading import Timer

def check_dependencies():
    """Проверка зависимостей"""
    required_modules = [
        'flask',
        'flask_cors',
        'selenium',
        'bs4',  # beautifulsoup4 импортируется как bs4
        'curl_cffi'
    ]
    
    missing_modules = []
    
    for module in required_modules:
        try:
            __import__(module)
        except ImportError:
            missing_modules.append(module)
    
    if missing_modules:
        print("❌ Отсутствуют зависимости:")
        for module in missing_modules:
            print(f"  - {module}")
        print("\n💡 Установите зависимости командой:")
        print("pip install -r requirements.txt")
        return False
    
    print("✅ Все зависимости установлены")
    return True

def open_browser():
    """Открытие браузера через 3 секунды"""
    time.sleep(3)
    try:
        webbrowser.open('http://localhost:5000')
        print("🌐 Браузер открыт: http://localhost:5000")
    except Exception as e:
        print(f"⚠️ Не удалось открыть браузер: {e}")
        print("📱 Откройте вручную: http://localhost:5000")

def main():
    """Основная функция"""
    print("🚀 Запуск админ панели Uzum Parser")
    print("=" * 50)
    
    # Проверяем зависимости
    if not check_dependencies():
        sys.exit(1)
    
    # Проверяем наличие файлов
    required_files = [
        'admin_panel.py',
        'templates/admin.html',
        'get_uzum_product_info.py',
        'uzum_parser.py'
    ]
    
    missing_files = []
    for file_path in required_files:
        if not os.path.exists(file_path):
            missing_files.append(file_path)
    
    if missing_files:
        print("❌ Отсутствуют файлы:")
        for file_path in missing_files:
            print(f"  - {file_path}")
        sys.exit(1)
    
    print("✅ Все файлы найдены")
    
    # Создаем папки если их нет
    os.makedirs('exports', exist_ok=True)
    os.makedirs('logs', exist_ok=True)
    
    print("📁 Папки созданы")
    
    # Запускаем браузер через 3 секунды
    timer = Timer(3.0, open_browser)
    timer.start()
    
    print("\n🌟 Админ панель запускается...")
    print("📊 Интерфейс будет доступен по адресу: http://localhost:5000")
    print("🔧 Для остановки нажмите Ctrl+C")
    print("\n" + "=" * 50)
    
    # Запускаем Flask приложение
    try:
        from admin_panel import app
        app.run(debug=False, host='0.0.0.0', port=5000)
    except KeyboardInterrupt:
        print("\n\n👋 Админ панель остановлена")
    except Exception as e:
        print(f"\n❌ Ошибка запуска: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
