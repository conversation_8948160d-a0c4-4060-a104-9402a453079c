#!/usr/bin/env python3
"""
Скрипт для исправления селекторов парсера Uzum
Анализирует реальную структуру сайта и обновляет селекторы
"""

import time
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager
from selenium_stealth import stealth
from bs4 import BeautifulSoup
from curl_cffi import requests

def setup_driver():
    """Настройка драйвера"""
    options = webdriver.ChromeOptions()
    options.add_argument('--headless')
    options.add_argument('--no-sandbox')
    options.add_argument('--disable-dev-shm-usage')
    
    service = Service(ChromeDriverManager().install())
    driver = webdriver.Chrome(service=service, options=options)
    
    stealth(driver,
            languages=["ru-RU", "ru", "en-US", "en"],
            vendor="Google Inc.",
            platform="Win32",
            webgl_vendor="Intel Inc.",
            renderer="Intel Iris OpenGL Engine",
            fix_hairline=True)
    
    return driver

def analyze_search_page():
    """Анализ структуры страницы поиска"""
    print("🔍 Анализ структуры страницы поиска")
    print("-" * 50)
    
    driver = setup_driver()
    
    try:
        # Переходим на страницу поиска
        search_url = "https://uzum.uz/ru/search?q=триммер"
        print(f"📡 Переход на: {search_url}")
        
        driver.get(search_url)
        time.sleep(5)
        
        # Прокручиваем для загрузки товаров
        for i in range(5):
            driver.execute_script('window.scrollBy(0, 500)')
            time.sleep(0.5)
        
        soup = BeautifulSoup(driver.page_source, 'html.parser')
        
        # Сохраняем HTML для анализа
        with open('search_page_analysis.html', 'w', encoding='utf-8') as f:
            f.write(driver.page_source)
        print("💾 HTML сохранен в search_page_analysis.html")
        
        # Анализируем возможные селекторы для карточек товаров
        print("\n🔍 Поиск карточек товаров:")
        
        # Различные варианты селекторов
        selectors_to_test = [
            # Стандартные селекторы
            '[data-testid="product-card"]',
            '.product-card',
            '[class*="product"]',
            '[class*="card"]',
            '[class*="item"]',
            
            # Ссылки на товары
            'a[href*="/product/"]',
            'a[href*="/goods/"]',
            'a[href*="/ru/product/"]',
            
            # Общие контейнеры
            '[class*="grid"] > div',
            '[class*="list"] > div',
            '[class*="catalog"] div',
            
            # Возможные новые селекторы
            '[data-product-id]',
            '[data-sku]',
            '.goods-tile',
            '.product-tile',
            '.catalog-item'
        ]
        
        best_selector = None
        max_found = 0
        
        for selector in selectors_to_test:
            try:
                elements = soup.select(selector)
                count = len(elements)
                
                if count > 0:
                    print(f"   ✅ {selector}: {count} элементов")
                    
                    if count > max_found:
                        max_found = count
                        best_selector = selector
                        
                        # Анализируем первый элемент
                        first_elem = elements[0]
                        print(f"      📦 Пример элемента:")
                        print(f"         Тег: {first_elem.name}")
                        print(f"         Классы: {first_elem.get('class', [])}")
                        
                        # Ищем название в элементе
                        title_found = False
                        title_selectors = ['h1', 'h2', 'h3', 'h4', 'h5', 'span', 'div', 'p']
                        for title_sel in title_selectors:
                            title_elem = first_elem.find(title_sel)
                            if title_elem and title_elem.get_text(strip=True):
                                text = title_elem.get_text(strip=True)
                                if len(text) > 5 and len(text) < 200:  # Разумная длина названия
                                    print(f"         Возможное название: {text[:50]}...")
                                    title_found = True
                                    break
                        
                        if not title_found:
                            print(f"         ⚠️ Название не найдено")
                
            except Exception as e:
                print(f"   ❌ {selector}: ошибка - {e}")
        
        print(f"\n🎯 Лучший селектор: {best_selector} ({max_found} элементов)")
        
        return best_selector, max_found
        
    finally:
        driver.quit()

def analyze_product_page():
    """Анализ структуры страницы товара"""
    print("\n🛍️ Анализ структуры страницы товара")
    print("-" * 50)
    
    # Используем один из найденных URL товаров
    product_url = "https://uzum.uz/ru/product/bumaga-listovaya-dlya-106839"
    print(f"📡 Анализ товара: {product_url}")
    
    driver = setup_driver()
    
    try:
        driver.get(product_url)
        time.sleep(5)
        
        soup = BeautifulSoup(driver.page_source, 'html.parser')
        
        # Сохраняем HTML
        with open('product_page_analysis.html', 'w', encoding='utf-8') as f:
            f.write(driver.page_source)
        print("💾 HTML товара сохранен в product_page_analysis.html")
        
        # Анализируем элементы товара
        print("\n🔍 Поиск элементов товара:")
        
        # Название товара
        title_selectors = [
            'h1',
            '[data-testid="product-title"]',
            '[data-testid="product-name"]',
            '.product-title',
            '.product-name',
            '[class*="title"]',
            '[class*="name"]',
            'title'
        ]
        
        print("📝 Поиск названия:")
        for selector in title_selectors:
            try:
                elem = soup.select_one(selector)
                if elem:
                    text = elem.get_text(strip=True)
                    if text and len(text) > 3:
                        print(f"   ✅ {selector}: {text[:100]}...")
                        break
            except:
                pass
        
        # Цена товара
        price_selectors = [
            '[data-testid="price-current"]',
            '[data-testid="product-price"]',
            '.price-current',
            '.current-price',
            '.price',
            '[class*="price"]',
            '[class*="cost"]'
        ]
        
        print("\n💰 Поиск цены:")
        for selector in price_selectors:
            try:
                elem = soup.select_one(selector)
                if elem:
                    text = elem.get_text(strip=True)
                    if text and any(char.isdigit() for char in text):
                        print(f"   ✅ {selector}: {text}")
                        break
            except:
                pass
        
        # Изображение
        img_selectors = [
            'meta[property="og:image"]',
            '[data-testid="product-image"] img',
            '.product-image img',
            '.main-image img',
            'img[alt*="товар"]',
            'img[alt*="product"]'
        ]
        
        print("\n🖼️ Поиск изображения:")
        for selector in img_selectors:
            try:
                elem = soup.select_one(selector)
                if elem:
                    if selector.startswith('meta'):
                        src = elem.get('content', '')
                    else:
                        src = elem.get('src', '') or elem.get('data-src', '')
                    
                    if src:
                        print(f"   ✅ {selector}: {src[:100]}...")
                        break
            except:
                pass
        
    finally:
        driver.quit()

def generate_updated_selectors():
    """Генерация обновленных селекторов на основе анализа"""
    print("\n🔧 РЕКОМЕНДУЕМЫЕ ОБНОВЛЕНИЯ СЕЛЕКТОРОВ")
    print("=" * 60)
    
    # Анализируем страницы
    best_card_selector, card_count = analyze_search_page()
    analyze_product_page()
    
    print(f"\n📋 РЕКОМЕНДАЦИИ ДЛЯ ОБНОВЛЕНИЯ КОДА:")
    print("-" * 40)
    
    if best_card_selector and card_count > 0:
        print(f"1. Обновить селектор карточек товаров:")
        print(f"   Заменить в find_product_cards():")
        print(f"   '{best_card_selector}' - найдено {card_count} элементов")
    
    print(f"\n2. Проверить селекторы в get_uzum_product_info.py:")
    print(f"   - Обновить селекторы названий")
    print(f"   - Обновить селекторы цен")
    print(f"   - Обновить селекторы изображений")
    
    print(f"\n3. Возможные причины проблем:")
    print(f"   - Сайт использует динамическую загрузку (JavaScript)")
    print(f"   - Изменилась структура HTML")
    print(f"   - Нужно больше времени для загрузки страницы")
    print(f"   - Требуется обход дополнительной защиты")

if __name__ == "__main__":
    print("🔧 АНАЛИЗ И ИСПРАВЛЕНИЕ СЕЛЕКТОРОВ UZUM PARSER")
    print("=" * 60)
    
    generate_updated_selectors()
    
    print(f"\n💡 Следующие шаги:")
    print(f"   1. Изучите сохраненные HTML файлы")
    print(f"   2. Обновите селекторы в коде парсера")
    print(f"   3. Протестируйте обновленный парсер")
    print(f"   4. Запустите test_search_diagnostic.py для проверки")
