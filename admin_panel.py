    #!/usr/bin/env python3
"""
Админ панель для Uzum Parser
Веб-интерфейс для управления и мониторинга парсера
"""

import os
import json
import threading
import time
import logging
from datetime import datetime
from flask import Flask, render_template, request, jsonify, send_from_directory
from flask_cors import CORS

# Импорт модулей парсера
try:
    from get_uzum_product_info import get_uzum_product_info
    from uzum_parser import init_webdriver, get_mainpage_cards, get_searchpage_cards
except ImportError as e:
    print(f"⚠️ Ошибка импорта модулей парсера: {e}")
    print("💡 Убедитесь, что все файлы парсера находятся в той же папке")

app = Flask(__name__)
CORS(app)

# Настройка логирования
def setup_logging():
    """Настройка системы логирования"""
    # Создаем папку для логов
    os.makedirs('logs', exist_ok=True)

    # Настраиваем форматирование
    log_format = '%(asctime)s - %(levelname)s - %(message)s'
    date_format = '%Y-%m-%d %H:%M:%S'

    # Настраиваем логгер для файла
    file_handler = logging.FileHandler('logs/uzum_parser.log', encoding='utf-8')
    file_handler.setLevel(logging.INFO)
    file_handler.setFormatter(logging.Formatter(log_format, date_format))

    # Настраиваем логгер для консоли
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    console_handler.setFormatter(logging.Formatter(log_format, date_format))

    # Настраиваем основной логгер
    logger = logging.getLogger('uzum_parser')
    logger.setLevel(logging.INFO)
    logger.addHandler(file_handler)
    logger.addHandler(console_handler)

    return logger

# Инициализируем логгер
logger = setup_logging()
logger.info("🚀 Админ панель Uzum Parser запущена")

# Глобальные переменные для хранения состояния
parser_status = {
    'running': False,
    'last_run': None,
    'total_products': 0,
    'errors': 0,
    'current_task': None
}

results_cache = {
    'main_page': [],
    'search_results': {},
    'last_updated': None
}

# Хранилище логов в памяти (последние 100 записей)
log_storage = []
MAX_LOGS = 100

def add_log(level, message):
    """Добавление лога в хранилище"""
    log_entry = {
        'timestamp': datetime.now().isoformat(),
        'level': level,
        'message': message
    }

    log_storage.append(log_entry)

    # Ограничиваем количество логов
    if len(log_storage) > MAX_LOGS:
        log_storage.pop(0)

    # Логируем в файл
    if level == 'INFO':
        logger.info(message)
    elif level == 'ERROR':
        logger.error(message)
    elif level == 'WARNING':
        logger.warning(message)
    elif level == 'DEBUG':
        logger.debug(message)

@app.route('/')
def index():
    """Главная страница админ панели"""
    return render_template('admin.html')

@app.route('/api/status')
def get_status():
    """Получение текущего статуса парсера"""
    return jsonify(parser_status)

@app.route('/api/results')
def get_results():
    """Получение результатов парсинга"""
    return jsonify(results_cache)

@app.route('/api/test-product', methods=['POST'])
def test_product():
    """Тестирование парсинга одного товара"""
    data = request.get_json()
    product_url = data.get('url', '')

    add_log('INFO', f'Запуск тестирования товара: {product_url}')

    if not product_url:
        add_log('ERROR', 'URL товара не указан')
        return jsonify({'error': 'URL товара не указан'}), 400

    try:
        add_log('INFO', f'Начало парсинга товара: {product_url}')
        product_info = get_uzum_product_info(product_url)

        if product_info:
            add_log('INFO', f'Товар успешно обработан: {product_info.get("product_id", "unknown")}')
            return jsonify({
                'success': True,
                'product': product_info,
                'timestamp': datetime.now().isoformat()
            })
        else:
            add_log('ERROR', f'Не удалось получить информацию о товаре: {product_url}')
            return jsonify({'error': 'Не удалось получить информацию о товаре'}), 404

    except Exception as e:
        add_log('ERROR', f'Ошибка при тестировании товара {product_url}: {str(e)}')
        return jsonify({'error': str(e)}), 500

@app.route('/api/search', methods=['POST'])
def search_products():
    """Поиск товаров"""
    data = request.get_json()
    query = data.get('query', '')
    max_pages = data.get('max_pages', 1)
    
    if not query:
        return jsonify({'error': 'Поисковый запрос не указан'}), 400
    
    def run_search():
        global parser_status, results_cache

        parser_status['running'] = True
        parser_status['current_task'] = f'Поиск: {query}'
        parser_status['errors'] = 0

        add_log('INFO', f'Начало поиска товаров по запросу: "{query}", страниц: {max_pages}')

        try:
            add_log('INFO', 'Инициализация веб-драйвера для поиска')
            driver = init_webdriver()

            add_log('INFO', f'Запуск парсинга поиска: {query}')
            search_results = get_searchpage_cards(driver, query, max_pages)
            driver.quit()

            results_cache['search_results'][query] = search_results
            results_cache['last_updated'] = datetime.now().isoformat()

            parser_status['total_products'] = len(search_results)
            parser_status['last_run'] = datetime.now().isoformat()

            add_log('INFO', f'Поиск завершен. Найдено товаров: {len(search_results)}')

        except Exception as e:
            parser_status['errors'] += 1
            add_log('ERROR', f'Ошибка поиска "{query}": {str(e)}')
        finally:
            parser_status['running'] = False
            parser_status['current_task'] = None
            add_log('INFO', f'Поиск "{query}" завершен')
    
    # Запускаем поиск в отдельном потоке
    thread = threading.Thread(target=run_search)
    thread.daemon = True
    thread.start()

    add_log('INFO', f'Поиск "{query}" запущен в фоновом режиме')
    return jsonify({'message': f'Поиск "{query}" запущен', 'status': 'started'})

@app.route('/api/parse-main', methods=['POST'])
def parse_main_page():
    """Парсинг главной страницы"""
    def run_main_parse():
        global parser_status, results_cache

        parser_status['running'] = True
        parser_status['current_task'] = 'Парсинг главной страницы'
        parser_status['errors'] = 0

        add_log('INFO', 'Начало парсинга главной страницы uzum.uz')

        try:
            add_log('INFO', 'Инициализация веб-драйвера для главной страницы')
            driver = init_webdriver()

            add_log('INFO', 'Запуск парсинга главной страницы')
            main_cards = get_mainpage_cards(driver, "https://uzum.uz/ru")
            driver.quit()

            results_cache['main_page'] = main_cards
            results_cache['last_updated'] = datetime.now().isoformat()

            parser_status['total_products'] = len(main_cards)
            parser_status['last_run'] = datetime.now().isoformat()

            add_log('INFO', f'Парсинг главной страницы завершен. Найдено товаров: {len(main_cards)}')

        except Exception as e:
            parser_status['errors'] += 1
            add_log('ERROR', f'Ошибка парсинга главной страницы: {str(e)}')
        finally:
            parser_status['running'] = False
            parser_status['current_task'] = None
            add_log('INFO', 'Парсинг главной страницы завершен')
    
    # Запускаем парсинг в отдельном потоке
    thread = threading.Thread(target=run_main_parse)
    thread.daemon = True
    thread.start()

    add_log('INFO', 'Парсинг главной страницы запущен в фоновом режиме')
    return jsonify({'message': 'Парсинг главной страницы запущен', 'status': 'started'})

@app.route('/api/export', methods=['GET'])
def export_results():
    """Экспорт результатов в JSON"""
    try:
        filename = f"uzum_export_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        filepath = os.path.join('exports', filename)
        
        # Создаем папку exports если её нет
        os.makedirs('exports', exist_ok=True)
        
        # Сохраняем результаты
        export_data = {
            'export_time': datetime.now().isoformat(),
            'parser_status': parser_status,
            'results': results_cache
        }
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(export_data, f, ensure_ascii=False, indent=2)
        
        return send_from_directory('exports', filename, as_attachment=True)
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/clear-results', methods=['POST'])
def clear_results():
    """Очистка результатов"""
    global results_cache

    add_log('INFO', 'Очистка всех результатов парсинга')

    results_cache = {
        'main_page': [],
        'search_results': {},
        'last_updated': None
    }

    add_log('INFO', 'Результаты успешно очищены')
    return jsonify({'message': 'Результаты очищены'})

@app.route('/api/logs')
def get_logs():
    """Получение логов"""
    # Возвращаем логи из памяти (последние записи)
    return jsonify(list(reversed(log_storage)))

@app.route('/api/clear-logs', methods=['POST'])
def clear_logs():
    """Очистка логов в памяти"""
    global log_storage

    add_log('INFO', 'Очистка логов в памяти')
    log_storage.clear()

    # Добавляем запись о очистке
    add_log('INFO', 'Логи в памяти очищены')

    return jsonify({'message': 'Логи очищены'})

if __name__ == '__main__':
    print("🚀 Запуск админ панели Uzum Parser")
    print("📊 Доступ: http://localhost:5000")
    print("🔧 Для остановки нажмите Ctrl+C")
    
    # Создаем папку для шаблонов если её нет
    os.makedirs('templates', exist_ok=True)
    
    app.run(debug=True, host='0.0.0.0', port=5000)
