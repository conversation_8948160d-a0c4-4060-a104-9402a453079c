#!/usr/bin/env python3
"""
Тестовый скрипт для проверки ChromeDriver
"""

def test_chromedriver():
    """Тестирование ChromeDriver"""
    print("🧪 Тестирование ChromeDriver")
    print("=" * 50)
    
    try:
        print("📦 Импорт модулей...")
        from selenium import webdriver
        from selenium.webdriver.chrome.service import Service
        from webdriver_manager.chrome import ChromeDriverManager
        from selenium_stealth import stealth
        print("✅ Модули импортированы успешно")
        
        print("\n🔧 Настройка Chrome опций...")
        options = webdriver.ChromeOptions()
        options.add_argument('--headless')  # Запуск в фоновом режиме
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-dev-shm-usage')
        options.add_argument('--disable-blink-features=AutomationControlled')
        options.add_experimental_option("excludeSwitches", ["enable-automation"])
        options.add_experimental_option('useAutomationExtension', False)
        print("✅ Chrome опции настроены")
        
        print("\n⬇️ Автоматическая установка ChromeDriver...")
        try:
            service = Service(ChromeDriverManager().install())
            print("✅ ChromeDriver установлен автоматически")
        except Exception as e:
            print(f"❌ Ошибка установки ChromeDriver: {e}")
            return False
        
        print("\n🚀 Запуск Chrome...")
        try:
            driver = webdriver.Chrome(service=service, options=options)
            print("✅ Chrome запущен успешно")
        except Exception as e:
            print(f"❌ Ошибка запуска Chrome: {e}")
            return False
        
        print("\n🔒 Применение stealth настроек...")
        try:
            stealth(driver,
                    languages=["ru-RU", "ru", "en-US", "en"],
                    vendor="Google Inc.",
                    platform="Win32",
                    webgl_vendor="Intel Inc.",
                    renderer="Intel Iris OpenGL Engine",
                    fix_hairline=True)
            print("✅ Stealth настройки применены")
        except Exception as e:
            print(f"⚠️ Ошибка stealth настроек: {e}")
        
        print("\n🌐 Тестирование доступа к сайту...")
        try:
            driver.get("https://uzum.uz/ru")
            title = driver.title
            print(f"✅ Сайт доступен. Заголовок: {title}")
            
            # Проверяем, что страница загрузилась
            if "uzum" in title.lower() or len(title) > 0:
                print("✅ Страница загружена корректно")
            else:
                print("⚠️ Возможно, страница загрузилась не полностью")
                
        except Exception as e:
            print(f"❌ Ошибка доступа к сайту: {e}")
            return False
        finally:
            print("\n🔚 Закрытие браузера...")
            driver.quit()
            print("✅ Браузер закрыт")
        
        print("\n" + "=" * 50)
        print("🎉 Тест ChromeDriver прошел успешно!")
        print("💡 Теперь можно запускать парсер")
        return True
        
    except ImportError as e:
        print(f"❌ Ошибка импорта: {e}")
        print("💡 Установите недостающие зависимости:")
        print("   pip install selenium webdriver-manager selenium-stealth")
        return False
    except Exception as e:
        print(f"❌ Неожиданная ошибка: {e}")
        return False

def check_chrome_version():
    """Проверка версии Chrome"""
    print("\n🔍 Проверка версии Chrome...")
    
    try:
        import subprocess
        result = subprocess.run([
            'reg', 'query', 
            'HKEY_CURRENT_USER\\Software\\Google\\Chrome\\BLBeacon', 
            '/v', 'version'
        ], capture_output=True, text=True, shell=True)
        
        if result.returncode == 0:
            for line in result.stdout.split('\n'):
                if 'version' in line and 'REG_SZ' in line:
                    version = line.split('REG_SZ')[-1].strip()
                    print(f"✅ Chrome версия: {version}")
                    return version
        
        print("⚠️ Не удалось определить версию Chrome")
        return None
        
    except Exception as e:
        print(f"❌ Ошибка проверки версии Chrome: {e}")
        return None

def main():
    """Основная функция"""
    print("🚀 Проверка готовности системы для парсинга")
    print("=" * 60)
    
    # Проверяем версию Chrome
    chrome_version = check_chrome_version()
    
    # Тестируем ChromeDriver
    success = test_chromedriver()
    
    if success:
        print("\n🎯 Система готова!")
        print("📋 Что можно делать дальше:")
        print("   1. python uzum_parser.py - запуск основного парсера")
        print("   2. python start_admin.py - запуск админ панели")
        print("   3. python demo_uzum_parser.py - демонстрация")
    else:
        print("\n❌ Система не готова")
        print("🔧 Рекомендации:")
        print("   1. Убедитесь, что Chrome установлен")
        print("   2. Установите зависимости: pip install -r requirements.txt")
        print("   3. Проверьте интернет-соединение")

if __name__ == "__main__":
    main()
